from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_POST
from django.utils import timezone
from datetime import datetime, timedelta
from django.db import transaction
import json
from .models import Leave, LeaveType, Attendance
from account.views import admin_required

# ============ LEAVE MANAGEMENT VIEWS ============

@login_required
def leave_apply(request):
    """Employee can apply for leave"""
    if request.method == 'POST':
        leave_type_id = request.POST.get('leave_type')
        start_date = request.POST.get('start_date')
        end_date = request.POST.get('end_date')
        reason = request.POST.get('reason')

        try:
            leave_type = LeaveType.objects.get(id=leave_type_id, is_active=True)

            # Validate dates
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()

            if start_date_obj > end_date_obj:
                messages.error(request, 'End date cannot be before start date.')
                return redirect('leave_apply')

            if start_date_obj < timezone.now().date():
                messages.error(request, 'Start date cannot be in the past.')
                return redirect('leave_apply')

            # Check for overlapping leaves
            overlapping_leaves = Leave.objects.filter(
                employee=request.user,
                status__in=['pending', 'approved'],
                is_deleted=False
            ).filter(
                Q(start_date__lte=end_date_obj, end_date__gte=start_date_obj)
            )

            if overlapping_leaves.exists():
                messages.error(request, 'You already have a leave application for overlapping dates.')
                return redirect('leave_apply')

            # Create leave application
            leave = Leave.objects.create(
                employee=request.user,
                leave_type=leave_type,
                start_date=start_date_obj,
                end_date=end_date_obj,
                reason=reason
            )

            messages.success(request, f'Leave application submitted successfully! Application ID: {leave.id}')
            return redirect('leave_history')

        except LeaveType.DoesNotExist:
            messages.error(request, 'Invalid leave type selected.')
        except ValueError:
            messages.error(request, 'Invalid date format.')
        except Exception as e:
            messages.error(request, f'Error submitting leave application: {str(e)}')

    leave_types = LeaveType.objects.filter(is_active=True)
    context = {
        'leave_types': leave_types,
    }
    return render(request, 'core/leave_apply.html', context)

@login_required
def leave_history(request):
    """Employee can view their leave history"""
    leaves = Leave.objects.filter(
        employee=request.user,
        is_deleted=False
    ).select_related('leave_type', 'approved_by')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        leaves = leaves.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        leaves = leaves.filter(
            Q(leave_type__name__icontains=search_query) |
            Q(reason__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(leaves, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_leaves = leaves.count()
    pending_leaves = leaves.filter(status='pending').count()
    approved_leaves = leaves.filter(status='approved').count()
    rejected_leaves = leaves.filter(status='rejected').count()

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'total_leaves': total_leaves,
        'pending_leaves': pending_leaves,
        'approved_leaves': approved_leaves,
        'rejected_leaves': rejected_leaves,
    }
    return render(request, 'core/leave_history.html', context)

@login_required
@admin_required
def leave_admin_list(request):
    """Admin can view all leave applications"""
    leaves = Leave.objects.filter(is_deleted=False).select_related('employee', 'leave_type', 'approved_by')

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        leaves = leaves.filter(status=status_filter)

    # Filter by employee
    employee_filter = request.GET.get('employee')
    if employee_filter:
        leaves = leaves.filter(employee__id=employee_filter)

    # Filter by leave type
    leave_type_filter = request.GET.get('leave_type')
    if leave_type_filter:
        leaves = leaves.filter(leave_type__id=leave_type_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        leaves = leaves.filter(
            Q(employee__username__icontains=search_query) |
            Q(employee__first_name__icontains=search_query) |
            Q(employee__last_name__icontains=search_query) |
            Q(reason__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(leaves, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    from django.contrib.auth import get_user_model
    User = get_user_model()
    employees = User.objects.filter(role='employee', is_active=True, is_deleted=False)
    leave_types = LeaveType.objects.filter(is_active=True)

    # Statistics
    total_leaves = leaves.count()
    pending_leaves = leaves.filter(status='pending').count()
    approved_leaves = leaves.filter(status='approved').count()
    rejected_leaves = leaves.filter(status='rejected').count()

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'employee_filter': employee_filter,
        'leave_type_filter': leave_type_filter,
        'search_query': search_query,
        'employees': employees,
        'leave_types': leave_types,
        'total_leaves': total_leaves,
        'pending_leaves': pending_leaves,
        'approved_leaves': approved_leaves,
        'rejected_leaves': rejected_leaves,
    }
    return render(request, 'core/leave_admin_list.html', context)

@login_required
@admin_required
def leave_detail(request, leave_id):
    """Admin can view leave details and approve/reject"""
    leave = get_object_or_404(Leave, id=leave_id, is_deleted=False)

    context = {
        'leave': leave,
    }
    return render(request, 'core/leave_detail.html', context)

@login_required
@admin_required
@require_POST
def leave_approve(request, leave_id):
    """Admin can approve leave"""
    leave = get_object_or_404(Leave, id=leave_id, is_deleted=False)

    if leave.status != 'pending':
        return JsonResponse({'success': False, 'message': 'Leave is not in pending status.'})

    admin_comments = request.POST.get('admin_comments', '')

    with transaction.atomic():
        leave.status = 'approved'
        leave.approved_by = request.user
        leave.approved_on = timezone.now()
        leave.admin_comments = admin_comments
        leave.save()

        # Create attendance records for approved leave days
        current_date = leave.start_date
        while current_date <= leave.end_date:
            # Skip weekends (optional - you can modify this logic)
            if current_date.weekday() < 5:  # Monday = 0, Sunday = 6
                Attendance.objects.update_or_create(
                    employee=leave.employee,
                    date=current_date,
                    defaults={
                        'status': 'on_leave',
                        'notes': f'On {leave.leave_type.name} leave',
                        'marked_by': request.user
                    }
                )
            current_date += timedelta(days=1)

    messages.success(request, f'Leave application approved successfully!')
    return JsonResponse({'success': True, 'message': 'Leave approved successfully!'})

@login_required
@admin_required
@require_POST
def leave_reject(request, leave_id):
    """Admin can reject leave"""
    leave = get_object_or_404(Leave, id=leave_id, is_deleted=False)

    if leave.status != 'pending':
        return JsonResponse({'success': False, 'message': 'Leave is not in pending status.'})

    admin_comments = request.POST.get('admin_comments', '')

    leave.status = 'rejected'
    leave.approved_by = request.user
    leave.approved_on = timezone.now()
    leave.admin_comments = admin_comments
    leave.save()

    messages.success(request, f'Leave application rejected.')
    return JsonResponse({'success': True, 'message': 'Leave rejected successfully!'})

@login_required
@require_POST
def leave_cancel(request, leave_id):
    """Employee can cancel their own pending leave"""
    leave = get_object_or_404(Leave, id=leave_id, employee=request.user, is_deleted=False)

    if leave.status != 'pending':
        return JsonResponse({'success': False, 'message': 'Only pending leaves can be cancelled.'})

    leave.status = 'cancelled'
    leave.save()

    messages.success(request, 'Leave application cancelled successfully!')
    return JsonResponse({'success': True, 'message': 'Leave cancelled successfully!'})

@login_required
@admin_required
def leave_reports(request):
    """Admin can view leave reports"""
    # Date range filter
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date:
        start_date = (timezone.now() - timedelta(days=30)).date()
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get leave statistics
    leaves = Leave.objects.filter(
        start_date__gte=start_date,
        end_date__lte=end_date,
        is_deleted=False
    )

    # Leave type statistics
    leave_type_stats = leaves.values('leave_type__name').annotate(
        count=Count('id'),
        total_days=Sum('end_date') - Sum('start_date') + Count('id')
    ).order_by('-count')

    # Employee statistics
    employee_stats = leaves.values(
        'employee__username',
        'employee__first_name',
        'employee__last_name'
    ).annotate(
        count=Count('id'),
        approved_count=Count('id', filter=Q(status='approved')),
        pending_count=Count('id', filter=Q(status='pending')),
        rejected_count=Count('id', filter=Q(status='rejected'))
    ).order_by('-count')

    # Monthly statistics
    monthly_stats = leaves.extra(
        select={'month': "DATE_FORMAT(start_date, '%%Y-%%m')"}
    ).values('month').annotate(
        count=Count('id'),
        approved=Count('id', filter=Q(status='approved'))
    ).order_by('month')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_leaves': leaves.count(),
        'approved_leaves': leaves.filter(status='approved').count(),
        'pending_leaves': leaves.filter(status='pending').count(),
        'rejected_leaves': leaves.filter(status='rejected').count(),
        'leave_type_stats': leave_type_stats,
        'employee_stats': employee_stats,
        'monthly_stats': monthly_stats,
    }
    return render(request, 'core/leave_reports.html', context)

# ============ ATTENDANCE SYSTEM VIEWS ============

@login_required
def attendance_dashboard(request):
    """Employee attendance dashboard with check-in/check-out"""
    today = timezone.now().date()

    # Get today's attendance record
    today_attendance, created = Attendance.objects.get_or_create(
        employee=request.user,
        date=today,
        defaults={'status': 'absent'}
    )

    # Get recent attendance records
    recent_attendance = Attendance.objects.filter(
        employee=request.user,
        is_deleted=False
    ).order_by('-date')[:10]

    # Calculate monthly statistics
    current_month_start = today.replace(day=1)
    monthly_attendance = Attendance.objects.filter(
        employee=request.user,
        date__gte=current_month_start,
        date__lte=today,
        is_deleted=False
    )

    present_days = monthly_attendance.filter(status__in=['present', 'late']).count()
    absent_days = monthly_attendance.filter(status='absent').count()
    leave_days = monthly_attendance.filter(status='on_leave').count()
    total_work_hours = monthly_attendance.aggregate(
        total=Sum('work_hours')
    )['total'] or 0

    context = {
        'today_attendance': today_attendance,
        'recent_attendance': recent_attendance,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'total_work_hours': total_work_hours,
        'can_check_in': not today_attendance.check_in_time,
        'can_check_out': today_attendance.check_in_time and not today_attendance.check_out_time,
    }
    return render(request, 'core/attendance_dashboard.html', context)

@login_required
@require_POST
def attendance_check_in(request):
    """Employee check-in"""
    today = timezone.now().date()
    current_time = timezone.now().time()

    try:
        attendance, created = Attendance.objects.get_or_create(
            employee=request.user,
            date=today,
            defaults={'status': 'present'}
        )

        if attendance.check_in_time:
            return JsonResponse({'success': False, 'message': 'You have already checked in today.'})

        # Get location if provided
        location = request.POST.get('location', '')

        attendance.check_in_time = current_time
        attendance.check_in_location = location
        attendance.status = 'late' if attendance.is_late else 'present'
        attendance.save()

        messages.success(request, f'Checked in successfully at {current_time.strftime("%H:%M")}!')
        return JsonResponse({
            'success': True,
            'message': f'Checked in at {current_time.strftime("%H:%M")}',
            'check_in_time': current_time.strftime("%H:%M")
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'Error checking in: {str(e)}'})

@login_required
@require_POST
def attendance_check_out(request):
    """Employee check-out"""
    today = timezone.now().date()
    current_time = timezone.now().time()

    try:
        attendance = Attendance.objects.get(
            employee=request.user,
            date=today
        )

        if not attendance.check_in_time:
            return JsonResponse({'success': False, 'message': 'You must check in first.'})

        if attendance.check_out_time:
            return JsonResponse({'success': False, 'message': 'You have already checked out today.'})

        # Get location if provided
        location = request.POST.get('location', '')

        attendance.check_out_time = current_time
        attendance.check_out_location = location
        attendance.calculate_work_hours()  # This will update work_hours and status

        messages.success(request, f'Checked out successfully at {current_time.strftime("%H:%M")}!')
        return JsonResponse({
            'success': True,
            'message': f'Checked out at {current_time.strftime("%H:%M")}',
            'check_out_time': current_time.strftime("%H:%M"),
            'work_hours': float(attendance.work_hours),
            'overtime_hours': float(attendance.overtime_hours)
        })

    except Attendance.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'No check-in record found for today.'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'Error checking out: {str(e)}'})

@login_required
def attendance_history(request):
    """Employee can view their attendance history"""
    attendances = Attendance.objects.filter(
        employee=request.user,
        is_deleted=False
    ).order_by('-date')

    # Filter by month if provided
    month_filter = request.GET.get('month')
    if month_filter:
        try:
            year, month = month_filter.split('-')
            attendances = attendances.filter(date__year=year, date__month=month)
        except ValueError:
            pass

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        attendances = attendances.filter(status=status_filter)

    # Pagination
    paginator = Paginator(attendances, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_days = attendances.count()
    present_days = attendances.filter(status__in=['present', 'late']).count()
    absent_days = attendances.filter(status='absent').count()
    leave_days = attendances.filter(status='on_leave').count()
    total_hours = attendances.aggregate(total=Sum('work_hours'))['total'] or 0
    total_overtime = attendances.aggregate(total=Sum('overtime_hours'))['total'] or 0

    context = {
        'page_obj': page_obj,
        'month_filter': month_filter,
        'status_filter': status_filter,
        'total_days': total_days,
        'present_days': present_days,
        'absent_days': absent_days,
        'leave_days': leave_days,
        'total_hours': total_hours,
        'total_overtime': total_overtime,
    }
    return render(request, 'core/attendance_history.html', context)

@login_required
@admin_required
def attendance_admin_list(request):
    """Admin can view all employee attendance"""
    attendances = Attendance.objects.filter(is_deleted=False).select_related('employee', 'marked_by')

    # Filter by date
    date_filter = request.GET.get('date')
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            attendances = attendances.filter(date=filter_date)
        except ValueError:
            pass
    else:
        # Default to today
        attendances = attendances.filter(date=timezone.now().date())

    # Filter by employee
    employee_filter = request.GET.get('employee')
    if employee_filter:
        attendances = attendances.filter(employee__id=employee_filter)

    # Filter by status
    status_filter = request.GET.get('status')
    if status_filter:
        attendances = attendances.filter(status=status_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        attendances = attendances.filter(
            Q(employee__username__icontains=search_query) |
            Q(employee__first_name__icontains=search_query) |
            Q(employee__last_name__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(attendances, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get filter options
    from django.contrib.auth import get_user_model
    User = get_user_model()
    employees = User.objects.filter(role='employee', is_active=True, is_deleted=False)

    # Statistics
    total_records = attendances.count()
    present_count = attendances.filter(status__in=['present', 'late']).count()
    absent_count = attendances.filter(status='absent').count()
    leave_count = attendances.filter(status='on_leave').count()

    context = {
        'page_obj': page_obj,
        'date_filter': date_filter,
        'employee_filter': employee_filter,
        'status_filter': status_filter,
        'search_query': search_query,
        'employees': employees,
        'total_records': total_records,
        'present_count': present_count,
        'absent_count': absent_count,
        'leave_count': leave_count,
    }
    return render(request, 'core/attendance_admin_list.html', context)

@login_required
@admin_required
def attendance_reports(request):
    """Admin can view attendance reports"""
    # Date range filter
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date:
        start_date = (timezone.now() - timedelta(days=30)).date()
    else:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

    if not end_date:
        end_date = timezone.now().date()
    else:
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

    # Get attendance statistics
    attendances = Attendance.objects.filter(
        date__gte=start_date,
        date__lte=end_date,
        is_deleted=False
    )

    # Employee statistics
    employee_stats = attendances.values(
        'employee__username',
        'employee__first_name',
        'employee__last_name'
    ).annotate(
        total_days=Count('id'),
        present_days=Count('id', filter=Q(status__in=['present', 'late'])),
        absent_days=Count('id', filter=Q(status='absent')),
        leave_days=Count('id', filter=Q(status='on_leave')),
        total_hours=Sum('work_hours'),
        overtime_hours=Sum('overtime_hours')
    ).order_by('employee__username')

    # Daily statistics
    daily_stats = attendances.values('date').annotate(
        total_employees=Count('id'),
        present=Count('id', filter=Q(status__in=['present', 'late'])),
        absent=Count('id', filter=Q(status='absent')),
        on_leave=Count('id', filter=Q(status='on_leave'))
    ).order_by('date')

    # Department-wise statistics (if you have departments)
    status_stats = attendances.values('status').annotate(
        count=Count('id')
    ).order_by('status')

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_records': attendances.count(),
        'employee_stats': employee_stats,
        'daily_stats': daily_stats,
        'status_stats': status_stats,
        'total_work_hours': attendances.aggregate(total=Sum('work_hours'))['total'] or 0,
        'total_overtime_hours': attendances.aggregate(total=Sum('overtime_hours'))['total'] or 0,
    }
    return render(request, 'core/attendance_reports.html', context)

@login_required
@admin_required
@require_POST
def attendance_mark_manual(request):
    """Admin can manually mark attendance"""
    employee_id = request.POST.get('employee_id')
    date = request.POST.get('date')
    status = request.POST.get('status')
    notes = request.POST.get('notes', '')

    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        employee = User.objects.get(id=employee_id, role='employee')
        attendance_date = datetime.strptime(date, '%Y-%m-%d').date()

        attendance, created = Attendance.objects.update_or_create(
            employee=employee,
            date=attendance_date,
            defaults={
                'status': status,
                'notes': notes,
                'marked_by': request.user,
                'admin_notes': f'Manually marked by {request.user.username}'
            }
        )

        action = 'created' if created else 'updated'
        messages.success(request, f'Attendance {action} successfully for {employee.username}!')
        return JsonResponse({'success': True, 'message': f'Attendance {action} successfully!'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'Error marking attendance: {str(e)}'})