{% extends 'base.html' %}

{% block title %}Leave Details - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-file-alt me-3"></i>
                        Leave Application Details
                    </h1>
                    <p class="lead mb-0">Review and manage leave application</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Leave Details Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-info-circle text-primary me-2"></i>
                            Application Information
                        </h4>
                        <div>
                            {% if leave.status == 'pending' %}
                                <span class="badge bg-warning fs-6">Pending Review</span>
                            {% elif leave.status == 'approved' %}
                                <span class="badge bg-success fs-6">Approved</span>
                            {% elif leave.status == 'rejected' %}
                                <span class="badge bg-danger fs-6">Rejected</span>
                            {% elif leave.status == 'cancelled' %}
                                <span class="badge bg-secondary fs-6">Cancelled</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Application ID</label>
                            <p class="fw-bold">#{{ leave.id }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Applied On</label>
                            <p class="fw-bold">{{ leave.applied_on|date:"F d, Y g:i A" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Employee</label>
                            <p class="fw-bold">
                                {{ leave.employee.first_name }} {{ leave.employee.last_name|default:leave.employee.username }}
                                <br><small class="text-muted">{{ leave.employee.username }} ({{ leave.employee.email }})</small>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Leave Type</label>
                            <p class="fw-bold">
                                <span class="badge bg-info fs-6">{{ leave.leave_type.name }}</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Start Date</label>
                            <p class="fw-bold">{{ leave.start_date|date:"F d, Y" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">End Date</label>
                            <p class="fw-bold">{{ leave.end_date|date:"F d, Y" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Total Days</label>
                            <p class="fw-bold">
                                <span class="badge bg-primary fs-6">{{ leave.total_days }} days</span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Current Status</label>
                            <p class="fw-bold">
                                {% if leave.is_current %}
                                    <span class="badge bg-success">Currently on Leave</span>
                                {% else %}
                                    <span class="badge bg-secondary">Not Active</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Reason for Leave</label>
                            <div class="border rounded p-3 bg-light">
                                {{ leave.reason|linebreaks }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Approval Information -->
                {% if leave.approved_by or leave.admin_comments %}
                <div class="dashboard-card mt-4">
                    <h5 class="mb-3">
                        <i class="fas fa-user-check text-success me-2"></i>
                        Approval Information
                    </h5>
                    
                    <div class="row">
                        {% if leave.approved_by %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Reviewed By</label>
                            <p class="fw-bold">
                                {{ leave.approved_by.first_name }} {{ leave.approved_by.last_name|default:leave.approved_by.username }}
                                <br><small class="text-muted">{{ leave.approved_by.username }}</small>
                            </p>
                        </div>
                        {% endif %}
                        {% if leave.approved_on %}
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Reviewed On</label>
                            <p class="fw-bold">{{ leave.approved_on|date:"F d, Y g:i A" }}</p>
                        </div>
                        {% endif %}
                        {% if leave.admin_comments %}
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Admin Comments</label>
                            <div class="border rounded p-3 bg-light">
                                {{ leave.admin_comments|linebreaks }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <!-- Actions Sidebar -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        Actions
                    </h5>
                    
                    {% if leave.status == 'pending' %}
                    <div class="d-grid gap-2 mb-3">
                        <button class="btn btn-success" onclick="approveLeave({{ leave.id }})">
                            <i class="fas fa-check me-2"></i>Approve Leave
                        </button>
                        <button class="btn btn-danger" onclick="rejectLeave({{ leave.id }})">
                            <i class="fas fa-times me-2"></i>Reject Leave
                        </button>
                    </div>
                    <hr>
                    {% endif %}
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'leave_admin_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i>Back to List
                        </a>
                        <button class="btn btn-outline-info" onclick="printDetails()">
                            <i class="fas fa-print me-2"></i>Print Details
                        </button>
                        <button class="btn btn-outline-success" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Export PDF
                        </button>
                    </div>
                </div>
                
                <!-- Leave Type Information -->
                <div class="dashboard-card mt-3">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        Leave Type Info
                    </h5>
                    
                    <div class="mb-2">
                        <strong>{{ leave.leave_type.name }}</strong>
                    </div>
                    {% if leave.leave_type.description %}
                    <p class="text-muted small">{{ leave.leave_type.description }}</p>
                    {% endif %}
                    <div class="d-flex justify-content-between">
                        <span>Max Days/Year:</span>
                        <span class="badge bg-info">{{ leave.leave_type.max_days_per_year }}</span>
                    </div>
                </div>
                
                <!-- Timeline -->
                <div class="dashboard-card mt-3">
                    <h5 class="mb-3">
                        <i class="fas fa-history text-warning me-2"></i>
                        Timeline
                    </h5>
                    
                    <div class="timeline">
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker bg-primary rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">Application Submitted</h6>
                                <small class="text-muted">{{ leave.applied_on|date:"M d, Y g:i A" }}</small>
                            </div>
                        </div>
                        
                        {% if leave.approved_on %}
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker {% if leave.status == 'approved' %}bg-success{% else %}bg-danger{% endif %} rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">
                                    {% if leave.status == 'approved' %}Approved{% else %}Rejected{% endif %}
                                </h6>
                                <small class="text-muted">{{ leave.approved_on|date:"M d, Y g:i A" }}</small>
                            </div>
                        </div>
                        {% endif %}
                        
                        {% if leave.status == 'approved' and leave.start_date %}
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker bg-info rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">Leave Period</h6>
                                <small class="text-muted">{{ leave.start_date|date:"M d" }} - {{ leave.end_date|date:"M d, Y" }}</small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">Approve Leave</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    {% csrf_token %}
                    <input type="hidden" id="leaveId" name="leave_id" value="{{ leave.id }}">
                    <input type="hidden" id="actionType" name="action">
                    
                    <div class="mb-3">
                        <label for="admin_comments" class="form-label">Comments (Optional)</label>
                        <textarea class="form-control" id="admin_comments" name="admin_comments" rows="3" 
                                  placeholder="Add any comments for the employee..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAction">Confirm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function approveLeave(leaveId) {
    document.getElementById('actionType').value = 'approve';
    document.getElementById('approvalModalTitle').textContent = 'Approve Leave Application';
    document.getElementById('confirmAction').textContent = 'Approve';
    document.getElementById('confirmAction').className = 'btn btn-success';
    
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}

function rejectLeave(leaveId) {
    document.getElementById('actionType').value = 'reject';
    document.getElementById('approvalModalTitle').textContent = 'Reject Leave Application';
    document.getElementById('confirmAction').textContent = 'Reject';
    document.getElementById('confirmAction').className = 'btn btn-danger';
    
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}

document.getElementById('confirmAction').addEventListener('click', function() {
    const leaveId = document.getElementById('leaveId').value;
    const action = document.getElementById('actionType').value;
    const comments = document.getElementById('admin_comments').value;
    
    const url = action === 'approve' ? `/leave/admin/${leaveId}/approve/` : `/leave/admin/${leaveId}/reject/`;
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `admin_comments=${encodeURIComponent(comments)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing the request.');
    });
});

function printDetails() {
    window.print();
}

function exportToPDF() {
    alert('PDF export functionality would be implemented here using a library like jsPDF or server-side PDF generation.');
}
</script>
{% endblock %}
