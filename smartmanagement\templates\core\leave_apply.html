{% extends 'base.html' %}

{% block title %}Apply for Leave - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-calendar-plus me-3"></i>
                        Apply for Leave
                    </h1>
                    <p class="lead mb-0">Submit your leave application</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Leave Application Form -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-file-alt text-primary me-2"></i>
                        Leave Application Form
                    </h4>
                    
                    <form method="post" id="leaveForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="leave_type" class="form-label">Leave Type <span class="text-danger">*</span></label>
                                <select class="form-select" id="leave_type" name="leave_type" required>
                                    <option value="">Select Leave Type</option>
                                    {% for leave_type in leave_types %}
                                        <option value="{{ leave_type.id }}" data-max-days="{{ leave_type.max_days_per_year }}">
                                            {{ leave_type.name }} (Max: {{ leave_type.max_days_per_year }} days/year)
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Total Days</label>
                                <input type="text" class="form-control" id="total_days" readonly placeholder="Auto-calculated">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">Reason for Leave <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="reason" name="reason" rows="4" required 
                                      placeholder="Please provide a detailed reason for your leave application..."></textarea>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> Your leave application will be sent to the admin for approval. 
                            You will be notified once it's reviewed.
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'leave_history' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to History
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Application
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Quick Info Sidebar -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle text-info me-2"></i>
                        Leave Information
                    </h5>
                    
                    <div class="mb-3">
                        <h6>Available Leave Types:</h6>
                        {% for leave_type in leave_types %}
                            <div class="d-flex justify-content-between mb-2">
                                <span>{{ leave_type.name }}</span>
                                <span class="badge bg-info">{{ leave_type.max_days_per_year }} days</span>
                            </div>
                        {% endfor %}
                    </div>
                    
                    <div class="alert alert-warning">
                        <small>
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            <strong>Important:</strong> Leave applications should be submitted at least 2 days in advance.
                        </small>
                    </div>
                    
                    <div class="text-center">
                        <a href="{% url 'leave_history' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history me-1"></i>View Leave History
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const totalDaysInput = document.getElementById('total_days');
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    startDateInput.min = today;
    endDateInput.min = today;
    
    function calculateDays() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        
        if (startDate && endDate && endDate >= startDate) {
            const timeDiff = endDate.getTime() - startDate.getTime();
            const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
            totalDaysInput.value = dayDiff + ' days';
        } else {
            totalDaysInput.value = '';
        }
    }
    
    startDateInput.addEventListener('change', function() {
        endDateInput.min = this.value;
        calculateDays();
    });
    
    endDateInput.addEventListener('change', calculateDays);
    
    // Form validation
    document.getElementById('leaveForm').addEventListener('submit', function(e) {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        
        if (endDate < startDate) {
            e.preventDefault();
            alert('End date cannot be before start date.');
            return false;
        }
        
        if (startDate < new Date(today)) {
            e.preventDefault();
            alert('Start date cannot be in the past.');
            return false;
        }
    });
});
</script>
{% endblock %}
