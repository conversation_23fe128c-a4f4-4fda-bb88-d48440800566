from django.urls import path
from . import views

urlpatterns = [
    # Leave Management URLs
    path('leave/apply/', views.leave_apply, name='leave_apply'),
    path('leave/history/', views.leave_history, name='leave_history'),
    path('leave/cancel/<int:leave_id>/', views.leave_cancel, name='leave_cancel'),
    
    # Admin Leave Management URLs
    path('leave/admin/', views.leave_admin_list, name='leave_admin_list'),
    path('leave/admin/<int:leave_id>/', views.leave_detail, name='leave_detail'),
    path('leave/admin/<int:leave_id>/approve/', views.leave_approve, name='leave_approve'),
    path('leave/admin/<int:leave_id>/reject/', views.leave_reject, name='leave_reject'),
    path('leave/reports/', views.leave_reports, name='leave_reports'),
    
    # Attendance System URLs
    path('attendance/', views.attendance_dashboard, name='attendance_dashboard'),
    path('attendance/check-in/', views.attendance_check_in, name='attendance_check_in'),
    path('attendance/check-out/', views.attendance_check_out, name='attendance_check_out'),
    path('attendance/history/', views.attendance_history, name='attendance_history'),
    
    # Admin Attendance URLs
    path('attendance/admin/', views.attendance_admin_list, name='attendance_admin_list'),
    path('attendance/admin/mark/', views.attendance_mark_manual, name='attendance_mark_manual'),
    path('attendance/reports/', views.attendance_reports, name='attendance_reports'),
]
