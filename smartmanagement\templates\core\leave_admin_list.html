{% extends 'base.html' %}

{% block title %}Leave Management - Admin - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-users-cog me-3"></i>
                        Leave Management
                    </h1>
                    <p class="lead mb-0">Manage employee leave applications</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Cards -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-file-alt"></i>
                        <div>{{ total_leaves }}</div>
                    </div>
                    <div class="stat-label">Total Applications</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ pending_leaves }}</div>
                    </div>
                    <div class="stat-label">Pending Review</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-check-circle"></i>
                        <div>{{ approved_leaves }}</div>
                    </div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <div class="stat-number">
                        <i class="fas fa-times-circle"></i>
                        <div>{{ rejected_leaves }}</div>
                    </div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Leave Management Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            Employee Leave Applications
                        </h4>
                        <a href="{% url 'leave_reports' %}" class="btn btn-success">
                            <i class="fas fa-chart-bar me-2"></i>View Reports
                        </a>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Status</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                <select name="employee" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Employees</option>
                                    {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>
                                            {{ employee.first_name }} {{ employee.last_name|default:employee.username }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                <select name="leave_type" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Leave Types</option>
                                    {% for leave_type in leave_types %}
                                        <option value="{{ leave_type.id }}" {% if leave_type_filter == leave_type.id|stringformat:"s" %}selected{% endif %}>
                                            {{ leave_type.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                <input type="text" name="search" class="form-control me-2" 
                                       placeholder="Search..." value="{{ search_query }}">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Leave Applications Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Leave Type</th>
                                    <th>Duration</th>
                                    <th>Days</th>
                                    <th>Applied On</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in page_obj %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                                {% if leave.employee.first_name %}
                                                    {{ leave.employee.first_name.0 }}{{ leave.employee.last_name.0|default:'' }}
                                                {% else %}
                                                    {{ leave.employee.username.0|upper }}
                                                {% endif %}
                                            </div>
                                            <div>
                                                <strong>{{ leave.employee.first_name }} {{ leave.employee.last_name|default:leave.employee.username }}</strong>
                                                <br><small class="text-muted">{{ leave.employee.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ leave.leave_type.name }}</td>
                                    <td>
                                        {{ leave.start_date|date:"M d" }} - {{ leave.end_date|date:"M d, Y" }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ leave.total_days }} days</span>
                                    </td>
                                    <td>{{ leave.applied_on|date:"M d, Y" }}</td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif leave.status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif leave.status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'leave_detail' leave.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if leave.status == 'pending' %}
                                            <button class="btn btn-sm btn-success" onclick="approveLeave({{ leave.id }})">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectLeave({{ leave.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No leave applications found.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Leave applications pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">Approve Leave</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    {% csrf_token %}
                    <input type="hidden" id="leaveId" name="leave_id">
                    <input type="hidden" id="actionType" name="action">
                    
                    <div class="mb-3">
                        <label for="admin_comments" class="form-label">Comments (Optional)</label>
                        <textarea class="form-control" id="admin_comments" name="admin_comments" rows="3" 
                                  placeholder="Add any comments for the employee..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmAction">Confirm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function approveLeave(leaveId) {
    document.getElementById('leaveId').value = leaveId;
    document.getElementById('actionType').value = 'approve';
    document.getElementById('approvalModalTitle').textContent = 'Approve Leave Application';
    document.getElementById('confirmAction').textContent = 'Approve';
    document.getElementById('confirmAction').className = 'btn btn-success';
    
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}

function rejectLeave(leaveId) {
    document.getElementById('leaveId').value = leaveId;
    document.getElementById('actionType').value = 'reject';
    document.getElementById('approvalModalTitle').textContent = 'Reject Leave Application';
    document.getElementById('confirmAction').textContent = 'Reject';
    document.getElementById('confirmAction').className = 'btn btn-danger';
    
    const modal = new bootstrap.Modal(document.getElementById('approvalModal'));
    modal.show();
}

document.getElementById('confirmAction').addEventListener('click', function() {
    const leaveId = document.getElementById('leaveId').value;
    const action = document.getElementById('actionType').value;
    const comments = document.getElementById('admin_comments').value;
    
    const url = action === 'approve' ? `/leave/admin/${leaveId}/approve/` : `/leave/admin/${leaveId}/reject/`;
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `admin_comments=${encodeURIComponent(comments)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while processing the request.');
    });
});
</script>
{% endblock %}
