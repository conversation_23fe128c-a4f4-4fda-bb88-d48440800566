{% extends 'base.html' %}

{% block title %}Employee Dashboard - Smart Management System{% endblock %}

{% block content %}
<!-- Welcome Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-user me-3"></i>
                        Welcome, {{ user.first_name|default:user.username }}!
                    </h1>
                    <p class="lead mb-0">Employee Dashboard - Your workspace for productivity</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- User Profile Stats -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user"></i>
                        <div>{{ user.get_role_display }}</div>
                    </div>
                    <div class="stat-label">Your Role</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-calendar"></i>
                        <div>{{ user.created_at|timesince }}</div>
                    </div>
                    <div class="stat-label">Member Since</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ user.last_login|timesince|default:"Never" }}</div>
                    </div>
                    <div class="stat-label">Last Login</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card animate-on-scroll" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="stat-number">
                        <i class="fas fa-check-circle"></i>
                        <div>{% if user.is_active %}Active{% else %}Inactive{% endif %}</div>
                    </div>
                    <div class="stat-label">Account Status</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- User Profile Information -->
            <div class="col-lg-8 mb-4">
                <div class="dashboard-card animate-on-scroll">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-user-circle text-primary me-2"></i>
                            Your Profile Information
                        </h4>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <p class="fw-bold">{{ user.first_name }} {{ user.last_name|default:"Not provided" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Username</label>
                            <p class="fw-bold">{{ user.username }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="fw-bold">{{ user.email }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Role</label>
                            <p class="fw-bold">
                                <span class="badge {% if user.role == 'admin' %}bg-success{% else %}bg-info{% endif %}">
                                    {{ user.get_role_display }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Phone</label>
                            <p class="fw-bold">{{ user.phone|default:"Not provided" }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Date of Birth</label>
                            <p class="fw-bold">{{ user.date_of_birth|default:"Not provided" }}</p>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label text-muted">Address</label>
                            <p class="fw-bold">{{ user.address|default:"Not provided" }}</p>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Note:</strong> To update your profile information, please contact your administrator.
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="col-lg-4 mb-4">
                <div class="dashboard-card animate-on-scroll mb-4">
                    <h4 class="mb-4">
                        <i class="fas fa-user-cog text-primary me-2"></i>
                        Account Information
                    </h4>
                    <div class="mb-3">
                        <label class="form-label text-muted">Account Status</label>
                        <p class="fw-bold">
                            {% if user.is_active %}
                                <span class="badge bg-success fs-6">Active</span>
                            {% else %}
                                <span class="badge bg-warning fs-6">Inactive</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Member Since</label>
                        <p class="fw-bold">{{ user.created_at|date:"F d, Y" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Last Login</label>
                        <p class="fw-bold">{{ user.last_login|date:"F d, Y g:i A"|default:"Never" }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Profile Updated</label>
                        <p class="fw-bold">{{ user.updated_at|date:"F d, Y" }}</p>
                    </div>
                </div>

                <!-- Leave & Attendance Quick Access -->
                <div class="dashboard-card animate-on-scroll mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-calendar-alt text-success me-2"></i>
                        Leave & Attendance
                    </h5>
                    <div class="d-grid gap-2">
                        <a href="{% url 'attendance_dashboard' %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-clock me-2"></i>Attendance Dashboard
                        </a>
                        <a href="{% url 'leave_apply' %}" class="btn btn-success btn-sm">
                            <i class="fas fa-calendar-plus me-2"></i>Apply for Leave
                        </a>
                        <a href="{% url 'leave_history' %}" class="btn btn-info btn-sm">
                            <i class="fas fa-history me-2"></i>Leave History
                        </a>
                        <a href="{% url 'attendance_history' %}" class="btn btn-warning btn-sm">
                            <i class="fas fa-chart-line me-2"></i>Attendance History
                        </a>
                    </div>
                </div>

                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-shield-alt text-success me-2"></i>
                        Account Security
                    </h4>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Profile Completion</span>
                            <span class="badge bg-info">{{ profile_completion|default:"75" }}%</span>
                        </div>
                        <div class="progress mt-2">
                            <div class="progress-bar bg-info" style="width: {{ profile_completion|default:"75" }}%"></div>
                        </div>
                    </div>
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            {% if user.phone and user.address and user.date_of_birth %}
                                Your profile is complete! ✅
                            {% else %}
                                Contact admin to complete your profile
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Activity -->
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card animate-on-scroll">
                    <h4 class="mb-4">
                        <i class="fas fa-history text-primary me-2"></i>
                        Account Activity
                    </h4>
                    <div class="timeline">
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker bg-success rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">Account created</h6>
                                <small class="text-muted">{{ user.created_at|timesince }} ago</small>
                            </div>
                        </div>
                        {% if user.last_login %}
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker bg-primary rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">Last login</h6>
                                <small class="text-muted">{{ user.last_login|timesince }} ago</small>
                            </div>
                        </div>
                        {% endif %}
                        <div class="timeline-item d-flex mb-3">
                            <div class="timeline-marker bg-info rounded-circle me-3" style="width: 12px; height: 12px; margin-top: 6px;"></div>
                            <div>
                                <h6 class="mb-1">Profile last updated</h6>
                                <small class="text-muted">{{ user.updated_at|timesince }} ago</small>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-light mt-4">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <i class="fas fa-user-shield fa-2x text-primary mb-2"></i>
                                <h6>Secure Account</h6>
                                <small class="text-muted">Your account is protected</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-clock fa-2x text-success mb-2"></i>
                                <h6>Active Status</h6>
                                <small class="text-muted">Account is active and functional</small>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-users fa-2x text-info mb-2"></i>
                                <h6>Team Member</h6>
                                <small class="text-muted">Part of the organization</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function addNewTask() {
        const taskName = prompt('Enter task name:');
        if (taskName) {
            const taskList = document.querySelector('.task-list');
            const newTask = document.createElement('div');
            newTask.className = 'task-item d-flex align-items-center p-3 mb-3 border rounded';
            newTask.innerHTML = `
                <input type="checkbox" class="form-check-input me-3">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${taskName}</h6>
                    <small class="text-muted">Due: Today</small>
                </div>
                <span class="badge bg-info">New</span>
            `;
            taskList.appendChild(newTask);

            // Add event listener for the new checkbox
            const checkbox = newTask.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', handleTaskComplete);
        }
    }

    function clockIn() {
        const now = new Date();
        const time = now.toLocaleTimeString();
        alert(`Clocked in at ${time}`);
    }

    function requestLeave() {
        alert('Leave request form coming soon!');
    }

    function submitReport() {
        alert('Report submission form coming soon!');
    }

    function viewSchedule() {
        alert('Schedule view coming soon!');
    }

    function handleTaskComplete(event) {
        const taskItem = event.target.closest('.task-item');
        const taskTitle = taskItem.querySelector('h6');
        const badge = taskItem.querySelector('.badge');

        if (event.target.checked) {
            taskTitle.classList.add('text-decoration-line-through', 'text-muted');
            badge.className = 'badge bg-success';
            badge.textContent = 'Completed';
        } else {
            taskTitle.classList.remove('text-decoration-line-through', 'text-muted');
            badge.className = 'badge bg-info';
            badge.textContent = 'Pending';
        }
    }

    // Add event listeners to existing checkboxes
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('.task-item input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleTaskComplete);
        });

        // Animate progress bars on load
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.transition = 'width 1s ease-in-out';
                bar.style.width = width;
            }, 500);
        });
    });

    // Auto-save task states (simulation)
    setInterval(function() {
        const completedTasks = document.querySelectorAll('.task-item input[type="checkbox"]:checked').length;
        const totalTasks = document.querySelectorAll('.task-item input[type="checkbox"]').length;

        // Update completion percentage in stats
        const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        console.log(`Task completion: ${completionRate}%`);
    }, 5000);
</script>
{% endblock %}
