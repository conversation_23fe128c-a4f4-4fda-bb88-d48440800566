from django.contrib import admin
from .models import LeaveType, Leave, Attendance

@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'max_days_per_year', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['name']

@admin.register(Leave)
class LeaveAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'start_date', 'end_date', 'total_days', 'status', 'applied_on']
    list_filter = ['status', 'leave_type', 'applied_on', 'start_date']
    search_fields = ['employee__username', 'employee__first_name', 'employee__last_name', 'reason']
    date_hierarchy = 'start_date'
    ordering = ['-applied_on']
    readonly_fields = ['total_days', 'applied_on', 'created_at', 'updated_at']

    fieldsets = (
        ('Leave Information', {
            'fields': ('employee', 'leave_type', 'start_date', 'end_date', 'reason')
        }),
        ('Status & Approval', {
            'fields': ('status', 'approved_by', 'approved_on', 'admin_comments')
        }),
        ('System Information', {
            'fields': ('total_days', 'applied_on', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'date', 'check_in_time', 'check_out_time', 'work_hours', 'overtime_hours', 'status']
    list_filter = ['status', 'date', 'created_at']
    search_fields = ['employee__username', 'employee__first_name', 'employee__last_name']
    date_hierarchy = 'date'
    ordering = ['-date', 'employee__username']
    readonly_fields = ['total_hours_worked', 'is_late', 'created_at', 'updated_at']

    fieldsets = (
        ('Attendance Information', {
            'fields': ('employee', 'date', 'status')
        }),
        ('Time Tracking', {
            'fields': ('check_in_time', 'check_out_time', 'work_hours', 'overtime_hours', 'total_hours_worked')
        }),
        ('Location & Notes', {
            'fields': ('check_in_location', 'check_out_location', 'notes', 'admin_notes'),
            'classes': ('collapse',)
        }),
        ('System Information', {
            'fields': ('marked_by', 'is_late', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
