{% extends 'base.html' %}

{% block title %}Leave History - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-history me-3"></i>
                        Leave History
                    </h1>
                    <p class="lead mb-0">View your leave applications and status</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Cards -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-file-alt"></i>
                        <div>{{ total_leaves }}</div>
                    </div>
                    <div class="stat-label">Total Applications</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ pending_leaves }}</div>
                    </div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-check-circle"></i>
                        <div>{{ approved_leaves }}</div>
                    </div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <div class="stat-number">
                        <i class="fas fa-times-circle"></i>
                        <div>{{ rejected_leaves }}</div>
                    </div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Leave History Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            Your Leave Applications
                        </h4>
                        <a href="{% url 'leave_apply' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Apply for Leave
                        </a>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <form method="get" class="d-flex">
                                <select name="status" class="form-select me-2" onchange="this.form.submit()">
                                    <option value="">All Status</option>
                                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-8">
                            <form method="get" class="d-flex">
                                {% if status_filter %}<input type="hidden" name="status" value="{{ status_filter }}">{% endif %}
                                <input type="text" name="search" class="form-control me-2" 
                                       placeholder="Search by leave type or reason..." value="{{ search_query }}">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Leave Applications Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Leave Type</th>
                                    <th>Duration</th>
                                    <th>Days</th>
                                    <th>Applied On</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ leave.leave_type.name }}</strong>
                                    </td>
                                    <td>
                                        {{ leave.start_date|date:"M d, Y" }} - {{ leave.end_date|date:"M d, Y" }}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ leave.total_days }} days</span>
                                    </td>
                                    <td>{{ leave.applied_on|date:"M d, Y" }}</td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif leave.status == 'approved' %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif leave.status == 'rejected' %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% elif leave.status == 'cancelled' %}
                                            <span class="badge bg-secondary">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewLeaveDetails({{ leave.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        {% if leave.status == 'pending' %}
                                            <button class="btn btn-sm btn-outline-danger" onclick="cancelLeave({{ leave.id }})">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-3x mb-3"></i>
                                        <p>No leave applications found.</p>
                                        <a href="{% url 'leave_apply' %}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Apply for Your First Leave
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Leave history pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Leave Details Modal -->
<div class="modal fade" id="leaveDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Leave Application Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="leaveDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewLeaveDetails(leaveId) {
    // This would typically fetch leave details via AJAX
    // For now, we'll show a placeholder
    document.getElementById('leaveDetailsContent').innerHTML = `
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
            <p>Loading leave details...</p>
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('leaveDetailsModal'));
    modal.show();
    
    // Simulate loading
    setTimeout(() => {
        document.getElementById('leaveDetailsContent').innerHTML = `
            <p><strong>Leave ID:</strong> ${leaveId}</p>
            <p><strong>Status:</strong> <span class="badge bg-warning">Pending</span></p>
            <p><strong>Reason:</strong> Personal work</p>
            <p><strong>Admin Comments:</strong> Under review</p>
        `;
    }, 1000);
}

function cancelLeave(leaveId) {
    if (confirm('Are you sure you want to cancel this leave application?')) {
        fetch(`/leave/cancel/${leaveId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the leave.');
        });
    }
}
</script>
{% endblock %}
