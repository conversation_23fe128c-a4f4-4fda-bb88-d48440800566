{% extends 'base.html' %}

{% block title %}Attendance Reports - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-chart-line me-3"></i>
                        Attendance Reports
                    </h1>
                    <p class="lead mb-0">Comprehensive attendance analytics and insights</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Date Range Filter -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-filter text-primary me-2"></i>
                        Report Filters
                    </h5>
                    
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Generate Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Summary Statistics -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-calendar-check"></i>
                        <div>{{ total_records }}</div>
                    </div>
                    <div class="stat-label">Total Records</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ total_work_hours|floatformat:0 }}</div>
                    </div>
                    <div class="stat-label">Total Work Hours</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-business-time"></i>
                        <div>{{ total_overtime_hours|floatformat:0 }}</div>
                    </div>
                    <div class="stat-label">Overtime Hours</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="stat-number">
                        <i class="fas fa-percentage"></i>
                        <div>{% if total_records > 0 %}{% widthratio employee_stats|length total_records 100 %}{% else %}0{% endif %}</div>
                    </div>
                    <div class="stat-label">Avg Attendance %</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Reports Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Employee Statistics -->
            <div class="col-lg-8 mb-4">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-users text-primary me-2"></i>
                            Employee Attendance Summary
                        </h4>
                        <button class="btn btn-success btn-sm" onclick="exportToCSV()">
                            <i class="fas fa-download me-2"></i>Export CSV
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover" id="employeeStatsTable">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Total Days</th>
                                    <th>Present</th>
                                    <th>Absent</th>
                                    <th>On Leave</th>
                                    <th>Work Hours</th>
                                    <th>Overtime</th>
                                    <th>Attendance %</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stat in employee_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ stat.employee__first_name }} {{ stat.employee__last_name|default:stat.employee__username }}</strong>
                                        <br><small class="text-muted">{{ stat.employee__username }}</small>
                                    </td>
                                    <td>{{ stat.total_days }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ stat.present_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ stat.absent_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ stat.leave_days }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ stat.total_hours|floatformat:1 }}h</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ stat.overtime_hours|floatformat:1 }}h</span>
                                    </td>
                                    <td>
                                        {% if stat.total_days > 0 %}
                                            {% widthratio stat.present_days stat.total_days 100 as attendance_rate %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" style="width: {{ attendance_rate }}%">
                                                    {{ attendance_rate }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                                        <p>No attendance data found for the selected period.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Status Distribution -->
            <div class="col-lg-4 mb-4">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie text-success me-2"></i>
                        Status Distribution
                    </h5>
                    
                    {% for status in status_stats %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-capitalize">{{ status.status|title }}</span>
                            <span class="badge bg-info">{{ status.count }}</span>
                        </div>
                        <div class="progress">
                            {% if total_records > 0 %}
                                {% widthratio status.count total_records 100 as percentage %}
                                <div class="progress-bar 
                                    {% if status.status == 'present' %}bg-success
                                    {% elif status.status == 'late' %}bg-warning
                                    {% elif status.status == 'absent' %}bg-danger
                                    {% elif status.status == 'on_leave' %}bg-info
                                    {% else %}bg-secondary{% endif %}" 
                                    style="width: {{ percentage }}%">
                                    {{ percentage }}%
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-card mt-3">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'attendance_admin_list' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-2"></i>View All Records
                        </a>
                        <button class="btn btn-outline-success btn-sm" onclick="printReport()">
                            <i class="fas fa-print me-2"></i>Print Report
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="emailReport()">
                            <i class="fas fa-envelope me-2"></i>Email Report
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Daily Statistics Chart -->
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-bar text-primary me-2"></i>
                        Daily Attendance Trends
                    </h4>
                    
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Total Employees</th>
                                    <th>Present</th>
                                    <th>Absent</th>
                                    <th>On Leave</th>
                                    <th>Attendance Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for daily in daily_stats %}
                                <tr>
                                    <td>{{ daily.date|date:"M d, Y" }}</td>
                                    <td>{{ daily.total_employees }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ daily.present }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ daily.absent }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ daily.on_leave }}</span>
                                    </td>
                                    <td>
                                        {% if daily.total_employees > 0 %}
                                            {% widthratio daily.present daily.total_employees 100 as daily_rate %}
                                            <div class="progress" style="height: 15px; width: 100px;">
                                                <div class="progress-bar bg-success" style="width: {{ daily_rate }}%">
                                                    {{ daily_rate }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted">No daily statistics available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    const table = document.getElementById('employeeStatsTable');
    let csv = [];
    
    // Get headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv.push(headers.join(','));
    
    // Get data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach((td, index) => {
            if (index === 7) { // Attendance % column
                const progressBar = td.querySelector('.progress-bar');
                if (progressBar) {
                    row.push(progressBar.textContent.trim());
                } else {
                    row.push('N/A');
                }
            } else {
                row.push(td.textContent.trim().replace(/\n/g, ' ').replace(/,/g, ';'));
            }
        });
        if (row.length > 0 && row[0] !== '') {
            csv.push(row.join(','));
        }
    });
    
    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `attendance_report_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function printReport() {
    window.print();
}

function emailReport() {
    alert('Email functionality would be implemented here. This would send the report to specified recipients.');
}

// Set default date range (last 30 days)
document.addEventListener('DOMContentLoaded', function() {
    const endDate = document.getElementById('end_date');
    const startDate = document.getElementById('start_date');
    
    if (!endDate.value) {
        endDate.value = new Date().toISOString().split('T')[0];
    }
    
    if (!startDate.value) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
