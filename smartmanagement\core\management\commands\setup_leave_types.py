from django.core.management.base import BaseCommand
from core.models import LeaveType

class Command(BaseCommand):
    help = 'Create default leave types'

    def handle(self, *args, **options):
        leave_types = [
            {
                'name': 'Annual Leave',
                'description': 'Yearly vacation leave',
                'max_days_per_year': 21
            },
            {
                'name': 'Sick Leave',
                'description': 'Medical leave for illness',
                'max_days_per_year': 10
            },
            {
                'name': 'Personal Leave',
                'description': 'Personal time off',
                'max_days_per_year': 5
            },
            {
                'name': 'Maternity Leave',
                'description': 'Maternity leave for new mothers',
                'max_days_per_year': 90
            },
            {
                'name': 'Paternity Leave',
                'description': 'Paternity leave for new fathers',
                'max_days_per_year': 15
            },
            {
                'name': 'Emergency Leave',
                'description': 'Emergency situations',
                'max_days_per_year': 3
            }
        ]

        for leave_type_data in leave_types:
            leave_type, created = LeaveType.objects.get_or_create(
                name=leave_type_data['name'],
                defaults={
                    'description': leave_type_data['description'],
                    'max_days_per_year': leave_type_data['max_days_per_year']
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created leave type: {leave_type.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Leave type already exists: {leave_type.name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully set up leave types!')
        )
