from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from datetime import datetime, time, timedelta

User = get_user_model()

class LeaveType(models.Model):
    """Model for different types of leaves"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    max_days_per_year = models.PositiveIntegerField(default=30)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class Leave(models.Model):
    """Model for leave applications"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('cancelled', 'Cancelled'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='leaves')
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE)
    start_date = models.DateField()
    end_date = models.DateField()
    reason = models.TextField()
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    applied_on = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_leaves')
    approved_on = models.DateTimeField(null=True, blank=True)
    admin_comments = models.TextField(blank=True, null=True)

    # Soft delete fields
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def total_days(self):
        """Calculate total days of leave"""
        return (self.end_date - self.start_date).days + 1

    @property
    def is_current(self):
        """Check if leave is currently active"""
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date and self.status == 'approved'

    def soft_delete(self):
        """Soft delete the leave"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def __str__(self):
        return f"{self.employee.username} - {self.leave_type.name} ({self.start_date} to {self.end_date})"

    class Meta:
        ordering = ['-applied_on']

class Attendance(models.Model):
    """Model for employee attendance tracking"""
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('half_day', 'Half Day'),
        ('on_leave', 'On Leave'),
    ]

    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='attendances')
    date = models.DateField()
    check_in_time = models.TimeField(null=True, blank=True)
    check_out_time = models.TimeField(null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='absent')
    work_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0.00)
    overtime_hours = models.DecimalField(max_digits=4, decimal_places=2, default=0.00)
    notes = models.TextField(blank=True, null=True)

    # Location tracking (optional)
    check_in_location = models.CharField(max_length=255, blank=True, null=True)
    check_out_location = models.CharField(max_length=255, blank=True, null=True)

    # Admin fields
    marked_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='marked_attendances')
    admin_notes = models.TextField(blank=True, null=True)

    # Soft delete fields
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def is_late(self):
        """Check if employee was late (assuming 9:00 AM is standard time)"""
        if self.check_in_time:
            standard_time = time(9, 0)  # 9:00 AM
            return self.check_in_time > standard_time
        return False

    @property
    def total_hours_worked(self):
        """Calculate total hours worked including overtime"""
        return float(self.work_hours) + float(self.overtime_hours)

    def calculate_work_hours(self):
        """Calculate work hours based on check-in and check-out times"""
        if self.check_in_time and self.check_out_time:
            # Convert times to datetime for calculation
            today = timezone.now().date()
            check_in_datetime = datetime.combine(today, self.check_in_time)
            check_out_datetime = datetime.combine(today, self.check_out_time)

            # Handle case where check-out is next day
            if self.check_out_time < self.check_in_time:
                check_out_datetime = datetime.combine(today + timedelta(days=1), self.check_out_time)

            total_time = check_out_datetime - check_in_datetime
            hours = total_time.total_seconds() / 3600

            # Standard work hours (8 hours)
            standard_hours = 8.0
            if hours <= standard_hours:
                self.work_hours = round(hours, 2)
                self.overtime_hours = 0.00
            else:
                self.work_hours = standard_hours
                self.overtime_hours = round(hours - standard_hours, 2)

            # Update status based on hours worked
            if hours >= 8:
                self.status = 'present'
            elif hours >= 4:
                self.status = 'half_day'
            elif self.is_late:
                self.status = 'late'

            self.save()

    def soft_delete(self):
        """Soft delete the attendance record"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()

    def __str__(self):
        return f"{self.employee.username} - {self.date} ({self.status})"

    class Meta:
        ordering = ['-date', '-created_at']
        unique_together = ['employee', 'date']
