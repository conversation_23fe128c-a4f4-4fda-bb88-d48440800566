{% extends 'base.html' %}

{% block title %}Attendance History - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-history me-3"></i>
                        Attendance History
                    </h1>
                    <p class="lead mb-0">View your attendance records and statistics</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Cards -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-calendar-check"></i>
                        <div>{{ present_days }}</div>
                    </div>
                    <div class="stat-label">Present Days</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <div class="stat-number">
                        <i class="fas fa-calendar-times"></i>
                        <div>{{ absent_days }}</div>
                    </div>
                    <div class="stat-label">Absent Days</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="stat-number">
                        <i class="fas fa-umbrella-beach"></i>
                        <div>{{ leave_days }}</div>
                    </div>
                    <div class="stat-label">Leave Days</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ total_hours|floatformat:0 }}</div>
                    </div>
                    <div class="stat-label">Total Hours</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Attendance History Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            Your Attendance Records
                        </h4>
                        <a href="{% url 'attendance_dashboard' %}" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>Back to Dashboard
                        </a>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <form method="get" class="d-flex">
                                <input type="month" name="month" class="form-control me-2" 
                                       value="{{ month_filter }}" onchange="this.form.submit()">
                            </form>
                        </div>
                        <div class="col-md-4">
                            <form method="get" class="d-flex">
                                {% if month_filter %}<input type="hidden" name="month" value="{{ month_filter }}">{% endif %}
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Status</option>
                                    <option value="present" {% if status_filter == 'present' %}selected{% endif %}>Present</option>
                                    <option value="late" {% if status_filter == 'late' %}selected{% endif %}>Late</option>
                                    <option value="absent" {% if status_filter == 'absent' %}selected{% endif %}>Absent</option>
                                    <option value="half_day" {% if status_filter == 'half_day' %}selected{% endif %}>Half Day</option>
                                    <option value="on_leave" {% if status_filter == 'on_leave' %}selected{% endif %}>On Leave</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex">
                                <span class="badge bg-info me-2">Total: {{ total_days }}</span>
                                <span class="badge bg-warning">Overtime: {{ total_overtime|floatformat:1 }}h</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Attendance Records Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Day</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Work Hours</th>
                                    <th>Overtime</th>
                                    <th>Status</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in page_obj %}
                                <tr>
                                    <td>
                                        <strong>{{ attendance.date|date:"M d, Y" }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ attendance.date|date:"l" }}</small>
                                    </td>
                                    <td>
                                        {% if attendance.check_in_time %}
                                            {{ attendance.check_in_time|time:"H:i" }}
                                            {% if attendance.is_late %}
                                                <small class="text-warning">
                                                    <i class="fas fa-exclamation-triangle" title="Late arrival"></i>
                                                </small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.check_out_time %}
                                            {{ attendance.check_out_time|time:"H:i" }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.work_hours %}
                                            <span class="badge bg-primary">{{ attendance.work_hours }}h</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.overtime_hours %}
                                            <span class="badge bg-warning">{{ attendance.overtime_hours }}h</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.status == 'present' %}
                                            <span class="badge bg-success">Present</span>
                                        {% elif attendance.status == 'late' %}
                                            <span class="badge bg-warning">Late</span>
                                        {% elif attendance.status == 'absent' %}
                                            <span class="badge bg-danger">Absent</span>
                                        {% elif attendance.status == 'half_day' %}
                                            <span class="badge bg-info">Half Day</span>
                                        {% elif attendance.status == 'on_leave' %}
                                            <span class="badge bg-secondary">On Leave</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.notes %}
                                            <small class="text-muted" title="{{ attendance.notes }}">
                                                <i class="fas fa-sticky-note"></i>
                                            </small>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                        <p>No attendance records found for the selected period.</p>
                                        <a href="{% url 'attendance_dashboard' %}" class="btn btn-primary">
                                            <i class="fas fa-clock me-2"></i>Go to Attendance Dashboard
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Attendance history pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if month_filter %}&month={{ month_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if month_filter %}&month={{ month_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if month_filter %}&month={{ month_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Summary Card -->
        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar text-success me-2"></i>
                        Summary Statistics
                    </h5>
                    
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success">{{ present_days }}</h4>
                                <small class="text-muted">Present Days</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-danger">{{ absent_days }}</h4>
                                <small class="text-muted">Absent Days</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary">{{ total_hours|floatformat:1 }}</h4>
                                <small class="text-muted">Total Hours</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning">{{ total_overtime|floatformat:1 }}</h4>
                            <small class="text-muted">Overtime Hours</small>
                        </div>
                    </div>
                    
                    {% if total_days > 0 %}
                    <div class="mt-4">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Attendance Rate</label>
                                <div class="progress">
                                    {% widthratio present_days total_days 100 as attendance_rate %}
                                    <div class="progress-bar bg-success" style="width: {{ attendance_rate }}%">
                                        {{ attendance_rate }}%
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Average Daily Hours</label>
                                <div class="progress">
                                    {% if present_days > 0 %}
                                        {% widthratio total_hours present_days 1 as avg_hours %}
                                        {% widthratio avg_hours 8 100 as avg_percentage %}
                                        <div class="progress-bar bg-info" style="width: {{ avg_percentage }}%">
                                            {{ avg_hours|floatformat:1 }}h
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Auto-set current month if no filter is applied
document.addEventListener('DOMContentLoaded', function() {
    const monthInput = document.querySelector('input[name="month"]');
    if (!monthInput.value) {
        const now = new Date();
        const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
        monthInput.value = currentMonth;
    }
});
</script>
{% endblock %}
