{% extends 'base.html' %}

{% block title %}Attendance Management - Admin - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-users-cog me-3"></i>
                        Attendance Management
                    </h1>
                    <p class="lead mb-0">Monitor and manage employee attendance</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Cards -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-users"></i>
                        <div>{{ total_records }}</div>
                    </div>
                    <div class="stat-label">Total Records</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user-check"></i>
                        <div>{{ present_count }}</div>
                    </div>
                    <div class="stat-label">Present Today</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <div class="stat-number">
                        <i class="fas fa-user-times"></i>
                        <div>{{ absent_count }}</div>
                    </div>
                    <div class="stat-label">Absent Today</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="stat-number">
                        <i class="fas fa-umbrella-beach"></i>
                        <div>{{ leave_count }}</div>
                    </div>
                    <div class="stat-label">On Leave</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Attendance Management Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            Employee Attendance Records
                        </h4>
                        <div>
                            <button class="btn btn-success me-2" onclick="showMarkAttendanceModal()">
                                <i class="fas fa-plus me-2"></i>Mark Attendance
                            </button>
                            <a href="{% url 'attendance_reports' %}" class="btn btn-info">
                                <i class="fas fa-chart-bar me-2"></i>View Reports
                            </a>
                        </div>
                    </div>
                    
                    <!-- Filters -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                <input type="date" name="date" class="form-control" 
                                       value="{{ date_filter|default:'' }}" onchange="this.form.submit()">
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                {% if date_filter %}<input type="hidden" name="date" value="{{ date_filter }}">{% endif %}
                                <select name="employee" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Employees</option>
                                    {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>
                                            {{ employee.first_name }} {{ employee.last_name|default:employee.username }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                {% if date_filter %}<input type="hidden" name="date" value="{{ date_filter }}">{% endif %}
                                {% if employee_filter %}<input type="hidden" name="employee" value="{{ employee_filter }}">{% endif %}
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">All Status</option>
                                    <option value="present" {% if status_filter == 'present' %}selected{% endif %}>Present</option>
                                    <option value="late" {% if status_filter == 'late' %}selected{% endif %}>Late</option>
                                    <option value="absent" {% if status_filter == 'absent' %}selected{% endif %}>Absent</option>
                                    <option value="half_day" {% if status_filter == 'half_day' %}selected{% endif %}>Half Day</option>
                                    <option value="on_leave" {% if status_filter == 'on_leave' %}selected{% endif %}>On Leave</option>
                                </select>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="get" class="d-flex">
                                {% if date_filter %}<input type="hidden" name="date" value="{{ date_filter }}">{% endif %}
                                {% if employee_filter %}<input type="hidden" name="employee" value="{{ employee_filter }}">{% endif %}
                                {% if status_filter %}<input type="hidden" name="status" value="{{ status_filter }}">{% endif %}
                                <input type="text" name="search" class="form-control me-2" 
                                       placeholder="Search employee..." value="{{ search_query }}">
                                <button type="submit" class="btn btn-outline-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Attendance Records Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Date</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Work Hours</th>
                                    <th>Overtime</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in page_obj %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar bg-primary text-white rounded-circle me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                                {% if attendance.employee.first_name %}
                                                    {{ attendance.employee.first_name.0 }}{{ attendance.employee.last_name.0|default:'' }}
                                                {% else %}
                                                    {{ attendance.employee.username.0|upper }}
                                                {% endif %}
                                            </div>
                                            <div>
                                                <strong>{{ attendance.employee.first_name }} {{ attendance.employee.last_name|default:attendance.employee.username }}</strong>
                                                <br><small class="text-muted">{{ attendance.employee.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ attendance.date|date:"M d, Y" }}</strong>
                                        <br><small class="text-muted">{{ attendance.date|date:"l" }}</small>
                                    </td>
                                    <td>
                                        {% if attendance.check_in_time %}
                                            {{ attendance.check_in_time|time:"H:i" }}
                                            {% if attendance.is_late %}
                                                <br><small class="text-warning"><i class="fas fa-exclamation-triangle"></i> Late</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.check_out_time %}
                                            {{ attendance.check_out_time|time:"H:i" }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.work_hours %}
                                            <span class="badge bg-primary">{{ attendance.work_hours }}h</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.overtime_hours %}
                                            <span class="badge bg-warning">{{ attendance.overtime_hours }}h</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.status == 'present' %}
                                            <span class="badge bg-success">Present</span>
                                        {% elif attendance.status == 'late' %}
                                            <span class="badge bg-warning">Late</span>
                                        {% elif attendance.status == 'absent' %}
                                            <span class="badge bg-danger">Absent</span>
                                        {% elif attendance.status == 'half_day' %}
                                            <span class="badge bg-info">Half Day</span>
                                        {% elif attendance.status == 'on_leave' %}
                                            <span class="badge bg-secondary">On Leave</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="editAttendance({{ attendance.id }}, '{{ attendance.employee.id }}', '{{ attendance.date|date:"Y-m-d" }}', '{{ attendance.status }}')">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if attendance.notes or attendance.admin_notes %}
                                            <button class="btn btn-sm btn-outline-info" onclick="viewNotes('{{ attendance.notes|default:"" }}', '{{ attendance.admin_notes|default:"" }}')">
                                                <i class="fas fa-sticky-note"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-calendar-times fa-3x mb-3"></i>
                                        <p>No attendance records found for the selected criteria.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Attendance records pagination">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Mark Attendance Modal -->
<div class="modal fade" id="markAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mark Attendance</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="markAttendanceForm">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="employee_id" class="form-label">Employee</label>
                        <select class="form-select" id="employee_id" name="employee_id" required>
                            <option value="">Select Employee</option>
                            {% for employee in employees %}
                                <option value="{{ employee.id }}">
                                    {{ employee.first_name }} {{ employee.last_name|default:employee.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="attendance_date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="attendance_date" name="date" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="attendance_status" class="form-label">Status</label>
                        <select class="form-select" id="attendance_status" name="status" required>
                            <option value="present">Present</option>
                            <option value="late">Late</option>
                            <option value="absent">Absent</option>
                            <option value="half_day">Half Day</option>
                            <option value="on_leave">On Leave</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="attendance_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="attendance_notes" name="notes" rows="3" 
                                  placeholder="Add any notes about this attendance record..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitMarkAttendance()">Mark Attendance</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('attendance_date').value = today;
    
    // Set date filter to today if not set
    const dateFilter = document.querySelector('input[name="date"]');
    if (!dateFilter.value) {
        dateFilter.value = today;
    }
});

function showMarkAttendanceModal() {
    const modal = new bootstrap.Modal(document.getElementById('markAttendanceModal'));
    modal.show();
}

function submitMarkAttendance() {
    const form = document.getElementById('markAttendanceForm');
    const formData = new FormData(form);
    
    fetch('/attendance/admin/mark/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while marking attendance.');
    });
}

function editAttendance(id, employeeId, date, status) {
    // Pre-fill the form with existing data
    document.getElementById('employee_id').value = employeeId;
    document.getElementById('attendance_date').value = date;
    document.getElementById('attendance_status').value = status;
    
    showMarkAttendanceModal();
}

function viewNotes(notes, adminNotes) {
    let content = '';
    if (notes) content += `<strong>Employee Notes:</strong><br>${notes}<br><br>`;
    if (adminNotes) content += `<strong>Admin Notes:</strong><br>${adminNotes}`;
    
    alert(content || 'No notes available');
}
</script>
{% endblock %}
