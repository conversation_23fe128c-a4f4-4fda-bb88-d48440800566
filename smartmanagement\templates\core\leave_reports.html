{% extends 'base.html' %}

{% block title %}Leave Reports - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-chart-bar me-3"></i>
                        Leave Reports
                    </h1>
                    <p class="lead mb-0">Comprehensive leave analytics and insights</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Date Range Filter -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-filter text-primary me-2"></i>
                        Report Filters
                    </h5>
                    
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>Generate Report
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Summary Statistics -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="stat-number">
                        <i class="fas fa-file-alt"></i>
                        <div>{{ total_leaves }}</div>
                    </div>
                    <div class="stat-label">Total Applications</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">
                        <i class="fas fa-check-circle"></i>
                        <div>{{ approved_leaves }}</div>
                    </div>
                    <div class="stat-label">Approved</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">
                        <i class="fas fa-clock"></i>
                        <div>{{ pending_leaves }}</div>
                    </div>
                    <div class="stat-label">Pending</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                    <div class="stat-number">
                        <i class="fas fa-times-circle"></i>
                        <div>{{ rejected_leaves }}</div>
                    </div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Reports Content -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <!-- Leave Type Statistics -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-pie text-primary me-2"></i>
                        Leave Type Distribution
                    </h4>
                    
                    {% for leave_type in leave_type_stats %}
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>{{ leave_type.leave_type__name }}</span>
                            <span class="badge bg-info">{{ leave_type.count }} applications</span>
                        </div>
                        <div class="progress">
                            {% if total_leaves > 0 %}
                                {% widthratio leave_type.count total_leaves 100 as percentage %}
                                <div class="progress-bar bg-primary" style="width: {{ percentage }}%">
                                    {{ percentage }}%
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-chart-pie fa-3x mb-3"></i>
                        <p>No leave type data available</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Monthly Trends -->
            <div class="col-lg-6 mb-4">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-chart-line text-success me-2"></i>
                        Monthly Trends
                    </h4>
                    
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Applications</th>
                                    <th>Approved</th>
                                    <th>Approval Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for monthly in monthly_stats %}
                                <tr>
                                    <td>{{ monthly.month }}</td>
                                    <td>{{ monthly.count }}</td>
                                    <td>{{ monthly.approved }}</td>
                                    <td>
                                        {% if monthly.count > 0 %}
                                            {% widthratio monthly.approved monthly.count 100 as approval_rate %}
                                            <div class="progress" style="height: 15px; width: 80px;">
                                                <div class="progress-bar bg-success" style="width: {{ approval_rate }}%">
                                                    {{ approval_rate }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No monthly data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Employee Statistics -->
        <div class="row">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-users text-primary me-2"></i>
                            Employee Leave Summary
                        </h4>
                        <button class="btn btn-success btn-sm" onclick="exportToCSV()">
                            <i class="fas fa-download me-2"></i>Export CSV
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover" id="employeeLeaveTable">
                            <thead>
                                <tr>
                                    <th>Employee</th>
                                    <th>Total Applications</th>
                                    <th>Approved</th>
                                    <th>Pending</th>
                                    <th>Rejected</th>
                                    <th>Approval Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for employee in employee_stats %}
                                <tr>
                                    <td>
                                        <strong>{{ employee.employee__first_name }} {{ employee.employee__last_name|default:employee.employee__username }}</strong>
                                        <br><small class="text-muted">{{ employee.employee__username }}</small>
                                    </td>
                                    <td>{{ employee.count }}</td>
                                    <td>
                                        <span class="badge bg-success">{{ employee.approved_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">{{ employee.pending_count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ employee.rejected_count }}</span>
                                    </td>
                                    <td>
                                        {% if employee.count > 0 %}
                                            {% widthratio employee.approved_count employee.count 100 as emp_approval_rate %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-success" style="width: {{ emp_approval_rate }}%">
                                                    {{ emp_approval_rate }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>No employee leave data found for the selected period.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{% url 'leave_admin_list' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-list me-2"></i>View All Leaves
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-success" onclick="printReport()">
                                    <i class="fas fa-print me-2"></i>Print Report
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-info" onclick="emailReport()">
                                    <i class="fas fa-envelope me-2"></i>Email Report
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-grid">
                                <a href="{% url 'attendance_reports' %}" class="btn btn-outline-warning">
                                    <i class="fas fa-clock me-2"></i>Attendance Reports
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function exportToCSV() {
    const table = document.getElementById('employeeLeaveTable');
    let csv = [];
    
    // Get headers
    const headers = [];
    table.querySelectorAll('thead th').forEach(th => {
        headers.push(th.textContent.trim());
    });
    csv.push(headers.join(','));
    
    // Get data rows
    table.querySelectorAll('tbody tr').forEach(tr => {
        const row = [];
        tr.querySelectorAll('td').forEach((td, index) => {
            if (index === 5) { // Approval Rate column
                const progressBar = td.querySelector('.progress-bar');
                if (progressBar) {
                    row.push(progressBar.textContent.trim());
                } else {
                    row.push('N/A');
                }
            } else {
                row.push(td.textContent.trim().replace(/\n/g, ' ').replace(/,/g, ';'));
            }
        });
        if (row.length > 0 && row[0] !== '') {
            csv.push(row.join(','));
        }
    });
    
    // Download CSV
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `leave_report_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function printReport() {
    window.print();
}

function emailReport() {
    alert('Email functionality would be implemented here. This would send the report to specified recipients.');
}

// Set default date range (last 30 days)
document.addEventListener('DOMContentLoaded', function() {
    const endDate = document.getElementById('end_date');
    const startDate = document.getElementById('start_date');
    
    if (!endDate.value) {
        endDate.value = new Date().toISOString().split('T')[0];
    }
    
    if (!startDate.value) {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        startDate.value = thirtyDaysAgo.toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
