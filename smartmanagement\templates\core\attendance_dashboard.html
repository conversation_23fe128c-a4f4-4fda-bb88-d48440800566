{% extends 'base.html' %}

{% block title %}Attendance Dashboard - Smart Management System{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-clock me-3"></i>
                        Attendance Dashboard
                    </h1>
                    <p class="lead mb-0">Track your daily attendance and work hours</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Today's Attendance Section -->
<section class="py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-calendar-day text-primary me-2"></i>
                        Today's Attendance - {{ today_attendance.date|date:"F d, Y" }}
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="attendance-card text-center p-4 border rounded">
                                <i class="fas fa-sign-in-alt fa-3x text-success mb-3"></i>
                                <h5>Check In</h5>
                                {% if today_attendance.check_in_time %}
                                    <p class="h4 text-success">{{ today_attendance.check_in_time|time:"H:i" }}</p>
                                    <small class="text-muted">
                                        {% if today_attendance.is_late %}
                                            <i class="fas fa-exclamation-triangle text-warning"></i> Late arrival
                                        {% else %}
                                            <i class="fas fa-check-circle text-success"></i> On time
                                        {% endif %}
                                    </small>
                                {% else %}
                                    <p class="text-muted">Not checked in</p>
                                    {% if can_check_in %}
                                        <button class="btn btn-success" onclick="checkIn()">
                                            <i class="fas fa-sign-in-alt me-2"></i>Check In
                                        </button>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="attendance-card text-center p-4 border rounded">
                                <i class="fas fa-sign-out-alt fa-3x text-danger mb-3"></i>
                                <h5>Check Out</h5>
                                {% if today_attendance.check_out_time %}
                                    <p class="h4 text-danger">{{ today_attendance.check_out_time|time:"H:i" }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-check-circle text-success"></i> Checked out
                                    </small>
                                {% else %}
                                    <p class="text-muted">Not checked out</p>
                                    {% if can_check_out %}
                                        <button class="btn btn-danger" onclick="checkOut()">
                                            <i class="fas fa-sign-out-alt me-2"></i>Check Out
                                        </button>
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if today_attendance.check_in_time and today_attendance.check_out_time %}
                    <div class="row">
                        <div class="col-md-4 text-center">
                            <div class="stat-item">
                                <h6 class="text-muted">Work Hours</h6>
                                <p class="h5 text-primary">{{ today_attendance.work_hours }} hrs</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="stat-item">
                                <h6 class="text-muted">Overtime</h6>
                                <p class="h5 text-warning">{{ today_attendance.overtime_hours }} hrs</p>
                            </div>
                        </div>
                        <div class="col-md-4 text-center">
                            <div class="stat-item">
                                <h6 class="text-muted">Status</h6>
                                <p class="h5">
                                    {% if today_attendance.status == 'present' %}
                                        <span class="badge bg-success">Present</span>
                                    {% elif today_attendance.status == 'late' %}
                                        <span class="badge bg-warning">Late</span>
                                    {% elif today_attendance.status == 'half_day' %}
                                        <span class="badge bg-info">Half Day</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Monthly Statistics -->
            <div class="col-lg-4">
                <div class="dashboard-card">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-pie text-success me-2"></i>
                        This Month's Summary
                    </h5>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Present Days</span>
                            <span class="badge bg-success">{{ present_days }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Absent Days</span>
                            <span class="badge bg-danger">{{ absent_days }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Leave Days</span>
                            <span class="badge bg-info">{{ leave_days }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Total Work Hours</span>
                            <span class="badge bg-primary">{{ total_work_hours|floatformat:1 }} hrs</span>
                        </div>
                    </div>
                    
                    <div class="text-center">
                        <a href="{% url 'attendance_history' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-history me-1"></i>View Full History
                        </a>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="dashboard-card mt-3">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        Quick Actions
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <a href="{% url 'leave_apply' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-calendar-plus me-2"></i>Apply for Leave
                        </a>
                        <a href="{% url 'leave_history' %}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-history me-2"></i>Leave History
                        </a>
                        <a href="{% url 'attendance_history' %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-clock me-2"></i>Attendance History
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Attendance -->
        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="dashboard-card">
                    <h4 class="mb-4">
                        <i class="fas fa-history text-primary me-2"></i>
                        Recent Attendance
                    </h4>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Work Hours</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in recent_attendance %}
                                <tr>
                                    <td>{{ attendance.date|date:"M d, Y" }}</td>
                                    <td>
                                        {% if attendance.check_in_time %}
                                            {{ attendance.check_in_time|time:"H:i" }}
                                            {% if attendance.is_late %}
                                                <small class="text-warning"><i class="fas fa-exclamation-triangle"></i></small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.check_out_time %}
                                            {{ attendance.check_out_time|time:"H:i" }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.work_hours %}
                                            {{ attendance.work_hours }} hrs
                                            {% if attendance.overtime_hours %}
                                                <small class="text-warning">(+{{ attendance.overtime_hours }} OT)</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if attendance.status == 'present' %}
                                            <span class="badge bg-success">Present</span>
                                        {% elif attendance.status == 'late' %}
                                            <span class="badge bg-warning">Late</span>
                                        {% elif attendance.status == 'absent' %}
                                            <span class="badge bg-danger">Absent</span>
                                        {% elif attendance.status == 'half_day' %}
                                            <span class="badge bg-info">Half Day</span>
                                        {% elif attendance.status == 'on_leave' %}
                                            <span class="badge bg-secondary">On Leave</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No attendance records found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function checkIn() {
    if (confirm('Are you sure you want to check in now?')) {
        // Get location if geolocation is available
        let location = '';
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                location = `${position.coords.latitude},${position.coords.longitude}`;
                performCheckIn(location);
            }, function() {
                performCheckIn('');
            });
        } else {
            performCheckIn('');
        }
    }
}

function performCheckIn(location) {
    fetch('/attendance/check-in/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `location=${encodeURIComponent(location)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while checking in.');
    });
}

function checkOut() {
    if (confirm('Are you sure you want to check out now?')) {
        // Get location if geolocation is available
        let location = '';
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                location = `${position.coords.latitude},${position.coords.longitude}`;
                performCheckOut(location);
            }, function() {
                performCheckOut('');
            });
        } else {
            performCheckOut('');
        }
    }
}

function performCheckOut(location) {
    fetch('/attendance/check-out/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `location=${encodeURIComponent(location)}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while checking out.');
    });
}

// Auto-refresh every 30 seconds to show current time
setInterval(function() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {hour12: false, hour: '2-digit', minute: '2-digit'});
    document.title = `${timeString} - Attendance Dashboard`;
}, 30000);
</script>
{% endblock %}
